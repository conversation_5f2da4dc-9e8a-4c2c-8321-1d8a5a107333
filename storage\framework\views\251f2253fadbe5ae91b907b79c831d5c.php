

<?php $__env->startSection('title', 'Dashboard'); ?>

<?php $__env->startSection('page_title', 'Dashboard Website'); ?>

<?php $__env->startPush('styles'); ?>
<style>
/* Statistik Card Styling */
.stats-card {
    transition: all 0.3s ease-in-out;
    border: none;
    border-radius: 15px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    overflow: hidden;
    min-height: 180px;
}

.stats-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 12px 30px rgba(0,0,0,0.2);
}

.stats-card .card-body {
    padding: 2rem 1.5rem;
    position: relative;
}

.stats-card .icon {
    opacity: 0.9;
    margin-bottom: 1rem;
}

.stats-card h2 {
    font-size: 2.5rem;
    font-weight: 700;
    margin: 0.5rem 0;
    text-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.stats-card p {
    font-size: 1rem;
    margin: 0;
    font-weight: 600;
}

.stats-card small {
    font-size: 0.85rem;
    line-height: 1.3;
}

/* Gradient Background untuk Card */
.bg-gradient-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.bg-gradient-success {
    background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
    color: white;
}

.bg-gradient-info {
    background: linear-gradient(135deg, #3b82f6 0%, #06b6d4 100%);
    color: white;
}

.bg-gradient-warning {
    background: linear-gradient(135deg, #f59e0b 0%, #eab308 100%);
    color: white;
}

.bg-gradient-secondary {
    background: linear-gradient(135deg, #8b5cf6 0%, #a855f7 100%);
    color: white;
}

.bg-gradient-dark {
    background: linear-gradient(135deg, #374151 0%, #6b7280 100%);
    color: white;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .stats-card .card-body {
        padding: 1.5rem 1rem;
    }

    .stats-card h2 {
        font-size: 2rem;
    }

    .stats-card .icon i {
        font-size: 2rem !important;
    }
}

/* Dashboard specific styles - tidak mengubah content-wrapper margin */
.dashboard-container {
    padding: 1rem;
    width: 100%;
    max-width: 100%;
    box-sizing: border-box;
}

/* Pastikan tidak ada overflow horizontal */
.dashboard-container .row {
    margin-left: -0.5rem;
    margin-right: -0.5rem;
}

.dashboard-container .col-lg-4,
.dashboard-container .col-md-6,
.dashboard-container .col-sm-12 {
    padding-left: 0.5rem;
    padding-right: 0.5rem;
}

/* Pastikan card tidak terpotong di mobile */
@media (max-width: 991.98px) {
    .dashboard-container {
        padding: 0.5rem;
    }

    .dashboard-container .row {
        margin-left: -0.25rem;
        margin-right: -0.25rem;
    }

    .dashboard-container .col-lg-4,
    .dashboard-container .col-md-6,
    .dashboard-container .col-sm-12 {
        padding-left: 0.25rem;
        padding-right: 0.25rem;
    }
}

/* Perbaikan khusus untuk AdminLTE */
.content-wrapper .dashboard-container {
    position: relative;
    z-index: 1;
}
</style>
<?php $__env->stopPush(); ?>

<?php $__env->startSection('content'); ?>
<div class="dashboard-container">
    <!-- Tambahkan card ucapan selamat datang -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card bg-gradient-info">
                <div class="card-body">
                    <h5 class="card-title">
                        <i class="fas fa-hand-wave mr-2"></i>
                        Selamat Datang di SIMAS Pelopor, <?php echo e(Auth::user()->name); ?>!
                    </h5>
                    <p class="card-text">
                        Sistem Informasi Manajemen Akademik Sekolah (SIMAS) Pelopor - Platform Digital Terpadu untuk Pengelolaan Akademik
                    </p>
                </div>
            </div>
        </div>
    </div>

<!-- Statistik Website -->
<div class="row mb-4">
    <div class="col-12">
        <h4 class="mb-3">
            <i class="fas fa-globe mr-2"></i>
            Statistik Konten Website
        </h4>
        <p class="text-muted mb-0">Ringkasan data konten website sekolah yang telah dipublikasikan</p>
    </div>
</div>

<!-- Baris Pertama: 3 Card -->
<div class="row mb-4">
    <!-- Card Event -->
    <div class="col-lg-4 col-md-6 col-sm-12 mb-3">
        <div class="card stats-card bg-gradient-primary h-100">
            <div class="card-body text-center">
                <div class="icon mb-3">
                    <i class="fas fa-calendar-alt fa-3x"></i>
                </div>
                <h2 class="card-title mb-2"><?php echo e($totalEvents ?? 0); ?></h2>
                <p class="card-text mb-1"><strong>Kegiatan & Event</strong></p>
                <small class="text-light opacity-75">Total kegiatan sekolah yang terdaftar</small>
            </div>
        </div>
    </div>

    <!-- Card Prestasi -->
    <div class="col-lg-4 col-md-6 col-sm-12 mb-3">
        <div class="card stats-card bg-gradient-success h-100">
            <div class="card-body text-center">
                <div class="icon mb-3">
                    <i class="fas fa-trophy fa-3x"></i>
                </div>
                <h2 class="card-title mb-2"><?php echo e($totalAchievements ?? 0); ?></h2>
                <p class="card-text mb-1"><strong>Prestasi & Penghargaan</strong></p>
                <small class="text-light opacity-75">Pencapaian siswa dan sekolah</small>
            </div>
        </div>
    </div>

    <!-- Card Fasilitas -->
    <div class="col-lg-4 col-md-6 col-sm-12 mb-3">
        <div class="card stats-card bg-gradient-info h-100">
            <div class="card-body text-center">
                <div class="icon mb-3">
                    <i class="fas fa-building fa-3x"></i>
                </div>
                <h2 class="card-title mb-2"><?php echo e($totalFacilities ?? 0); ?></h2>
                <p class="card-text mb-1"><strong>Fasilitas Sekolah</strong></p>
                <small class="text-light opacity-75">Sarana dan prasarana tersedia</small>
            </div>
        </div>
    </div>
</div>

<!-- Baris Kedua: 3 Card -->
<div class="row mb-4">
    <!-- Card Ekstrakurikuler -->
    <div class="col-lg-4 col-md-6 col-sm-12 mb-3">
        <div class="card stats-card bg-gradient-warning h-100">
            <div class="card-body text-center">
                <div class="icon mb-3">
                    <i class="fas fa-users fa-3x"></i>
                </div>
                <h2 class="card-title mb-2"><?php echo e($totalExtracurriculars ?? 0); ?></h2>
                <p class="card-text mb-1"><strong>Ekstrakurikuler</strong></p>
                <small class="text-light opacity-75">Kegiatan pengembangan bakat siswa</small>
            </div>
        </div>
    </div>

    <!-- Card Slide -->
    <div class="col-lg-4 col-md-6 col-sm-12 mb-3">
        <div class="card stats-card bg-gradient-secondary h-100">
            <div class="card-body text-center">
                <div class="icon mb-3">
                    <i class="fas fa-images fa-3x"></i>
                </div>
                <h2 class="card-title mb-2"><?php echo e($totalSlides ?? 0); ?></h2>
                <p class="card-text mb-1"><strong>Slide Banner</strong></p>
                <small class="text-light opacity-75">Gambar promosi di halaman utama</small>
            </div>
        </div>
    </div>

    <!-- Card Unit -->
    <div class="col-lg-4 col-md-6 col-sm-12 mb-3">
        <div class="card stats-card bg-gradient-dark h-100">
            <div class="card-body text-center">
                <div class="icon mb-3">
                    <i class="fas fa-school fa-3x"></i>
                </div>
                <h2 class="card-title mb-2"><?php echo e(isset($units) ? $units->count() : 1); ?></h2>
                <p class="card-text mb-1"><strong>Unit Sekolah</strong></p>
                <small class="opacity-75">Jenjang pendidikan yang tersedia</small>
            </div>
        </div>
    </div>
</div>

<?php if (\Illuminate\Support\Facades\Blade::check('role', 'Administrator|Yayasan|Pengawas')): ?>
<!-- Rincian Statistik Per Unit Sekolah (Khusus Administrator) -->
<?php if(isset($eventsPerUnit) || isset($achievementsPerUnit) || isset($extracurricularsPerUnit)): ?>
<div class="row mb-4">
    <div class="col-12">
        <h4 class="mb-3">
            <i class="fas fa-chart-bar mr-2"></i>
            Rincian Data Per Unit Sekolah
        </h4>
        <p class="text-muted mb-0">Distribusi konten website berdasarkan unit/jenjang pendidikan</p>
    </div>
</div>

<div class="row">
    <!-- Kegiatan Per Unit -->
    <?php if(isset($eventsPerUnit) && $eventsPerUnit->count() > 0): ?>
    <div class="col-lg-4 col-md-6 mb-4">
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-primary text-white">
                <h5 class="card-title mb-0">
                    <i class="fas fa-calendar-alt mr-2"></i>
                    Kegiatan & Event
                </h5>
                <small class="opacity-75">Distribusi kegiatan per unit</small>
            </div>
            <div class="card-body">
                <?php $__currentLoopData = $eventsPerUnit; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <div class="d-flex justify-content-between align-items-center mb-3 pb-2 border-bottom">
                    <div>
                        <span class="font-weight-medium"><?php echo e($item['unit']); ?></span>
                        <br><small class="text-muted">Unit Sekolah</small>
                    </div>
                    <span class="badge badge-primary badge-pill px-3 py-2"><?php echo e($item['total']); ?> kegiatan</span>
                </div>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </div>
        </div>
    </div>
    <?php endif; ?>

    <!-- Prestasi Per Unit -->
    <?php if(isset($achievementsPerUnit) && $achievementsPerUnit->count() > 0): ?>
    <div class="col-lg-4 col-md-6 mb-4">
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-success text-white">
                <h5 class="card-title mb-0">
                    <i class="fas fa-trophy mr-2"></i>
                    Prestasi & Penghargaan
                </h5>
                <small class="opacity-75">Pencapaian per unit</small>
            </div>
            <div class="card-body">
                <?php $__currentLoopData = $achievementsPerUnit; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <div class="d-flex justify-content-between align-items-center mb-3 pb-2 border-bottom">
                    <div>
                        <span class="font-weight-medium"><?php echo e($item['unit']); ?></span>
                        <br><small class="text-muted">Unit Sekolah</small>
                    </div>
                    <span class="badge badge-success badge-pill px-3 py-2"><?php echo e($item['total']); ?> prestasi</span>
                </div>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </div>
        </div>
    </div>
    <?php endif; ?>

    <!-- Ekstrakurikuler Per Unit -->
    <?php if(isset($extracurricularsPerUnit) && $extracurricularsPerUnit->count() > 0): ?>
    <div class="col-lg-4 col-md-6 mb-4">
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-warning text-white">
                <h5 class="card-title mb-0">
                    <i class="fas fa-users mr-2"></i>
                    Kegiatan Ekstrakurikuler
                </h5>
                <small class="opacity-75">Program pengembangan bakat</small>
            </div>
            <div class="card-body">
                <?php $__currentLoopData = $extracurricularsPerUnit; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <div class="d-flex justify-content-between align-items-center mb-3 pb-2 border-bottom">
                    <div>
                        <span class="font-weight-medium"><?php echo e($item['unit']); ?></span>
                        <br><small class="text-muted">Unit Sekolah</small>
                    </div>
                    <span class="badge badge-warning badge-pill px-3 py-2"><?php echo e($item['total']); ?> program</span>
                </div>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </div>
        </div>
    </div>
    <?php endif; ?>
</div>
<?php endif; ?>
<?php endif; ?>

<!-- Statistik Utama (Siswa, Guru, dll) -->
<!-- Bagian ini bisa ditambahkan sesuai kebutuhan untuk statistik akademik -->

</div> <!-- End dashboard-container -->
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.admin', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\xampp\htdocs\webplp\resources\views/admin/dashboard.blade.php ENDPATH**/ ?>