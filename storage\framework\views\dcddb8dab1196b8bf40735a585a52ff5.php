<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
    <url>
        <loc><?php echo e(url('/')); ?></loc>
        <lastmod><?php echo e(now()->toISOString()); ?></lastmod>
        <changefreq>daily</changefreq>
        <priority>1.0</priority>
    </url>
    
    <?php $__currentLoopData = $articles; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $article): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
    <url>
        <loc><?php echo e(route('website.artikel.show', $article->slug)); ?></loc>
        <lastmod><?php echo e($article->updated_at->toISOString()); ?></lastmod>
        <changefreq>weekly</changefreq>
        <priority>0.8</priority>
    </url>
    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
    
    <?php $__currentLoopData = $achievements; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $achievement): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
        <?php if($achievement->slug): ?>
        <url>
            <loc><?php echo e(route('website.prestasi.show', $achievement->slug)); ?></loc>
            <lastmod><?php echo e($achievement->updated_at->toISOString()); ?></lastmod>
            <changefreq>monthly</changefreq>
            <priority>0.7</priority>
        </url>
        <?php endif; ?>
    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
</urlset>

<?php /**PATH C:\xampp\htdocs\webplp\resources\views/sitemap/index.blade.php ENDPATH**/ ?>