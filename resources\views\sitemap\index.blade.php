<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
    <url>
        <loc>{{ url('/') }}</loc>
        <lastmod>{{ now()->toISOString() }}</lastmod>
        <changefreq>daily</changefreq>
        <priority>1.0</priority>
    </url>
    
    @foreach($articles as $article)
    <url>
        <loc>{{ route('website.artikel.show', $article->slug) }}</loc>
        <lastmod>{{ $article->updated_at->toISOString() }}</lastmod>
        <changefreq>weekly</changefreq>
        <priority>0.8</priority>
    </url>
    @endforeach
    
    @foreach($achievements as $achievement)
        @if($achievement->slug)
        <url>
            <loc>{{ route('website.prestasi.show', $achievement->slug) }}</loc>
            <lastmod>{{ $achievement->updated_at->toISOString() }}</lastmod>
            <changefreq>monthly</changefreq>
            <priority>0.7</priority>
        </url>
        @endif
    @endforeach
</urlset>

