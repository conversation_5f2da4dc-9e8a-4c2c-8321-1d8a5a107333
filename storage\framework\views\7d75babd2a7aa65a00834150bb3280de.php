<?php $__env->startSection('title', '<PERSON><PERSON><PERSON>'); ?>

<?php $__env->startSection('content'); ?>
<style>
.title-container {
    position: relative;
    padding: 20px 0;
    margin-bottom: 50px;
}

.facility-title {
    font-size: 2.5rem;
    font-weight: 800;
    color: #2c3e50;
    text-transform: uppercase;
    letter-spacing: 2px;
    margin-bottom: 20px;
    position: relative;
    display: inline-block;
    padding-bottom: 15px;
}

.facility-title::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 80px;
    height: 4px;
    background: linear-gradient(to right, #3498db, #2ecc71);
    border-radius: 2px;
}

.title-decoration {
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 150px;
    height: 2px;
    background-color: #ecf0f1;
}

.title-container::before,
.title-container::after {
    content: '★';
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    font-size: 24px;
    color: #3498db;
}

.title-container::before {
    left: 25%;
}

.title-container::after {
    right: 25%;
}

@media (max-width: 768px) {
    .facility-title {
        font-size: 2rem;
    }
    .title-container::before {
        left: 10%;
    }
    .title-container::after {
        right: 10%;
    }
}

.card {
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}
.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 20px rgba(0,0,0,0.1);
}
.card-img-top {
    height: 250px;
    object-fit: cover;
}
</style>
<div class="container py-5">
    <div class="title-container text-center">
        <h2 class="facility-title">Semua Prestasi</h2>
        <div class="title-decoration"></div>
    </div>

    <!-- Filter Jenjang -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="list-group list-group-horizontal-md justify-content-center">
                <a href="<?php echo e(route('website.prestasi.all')); ?>" 
                   class="list-group-item list-group-item-action <?php echo e(request()->routeIs('website.prestasi.all') ? 'active' : ''); ?>">
                    Semua
                </a>
                <a href="<?php echo e(route('website.prestasi.jenjang', 'paud')); ?>" 
                   class="list-group-item list-group-item-action <?php echo e(request()->segment(2) == 'paud' ? 'active' : ''); ?>">
                    PAUD
                </a>
                <a href="<?php echo e(route('website.prestasi.jenjang', 'sd')); ?>" 
                   class="list-group-item list-group-item-action <?php echo e(request()->segment(2) == 'sd' ? 'active' : ''); ?>">
                    SD
                </a>
                <a href="<?php echo e(route('website.prestasi.jenjang', 'smp')); ?>" 
                   class="list-group-item list-group-item-action <?php echo e(request()->segment(2) == 'smp' ? 'active' : ''); ?>">
                    SMP
                </a>
                <a href="<?php echo e(route('website.prestasi.jenjang', 'sma')); ?>" 
                   class="list-group-item list-group-item-action <?php echo e(request()->segment(2) == 'sma' ? 'active' : ''); ?>">
                    SMA
                </a>
            </div>
        </div>
    </div>

    <div class="row">
        <?php $__empty_1 = true; $__currentLoopData = $achievements; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $achievement): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
        <div class="col-md-4 mb-4">
            <div class="card h-100">
                <?php if($achievement->image): ?>
                    <img src="<?php echo e(asset('storage/prestasi/' . $achievement->image)); ?>" 
                         class="card-img-top" 
                         alt="<?php echo e($achievement->title); ?>">
                <?php endif; ?>
                <div class="card-body">
                    <h5 class="card-title"><?php echo e($achievement->title); ?></h5>
                    <p class="text-muted">
                        <i class="fas fa-calendar"></i> 
                        <?php echo e($achievement->tanggal ? $achievement->tanggal->format('d M Y') : '-'); ?>

                    </p>
                    <p class="text-muted">
                        <i class="fas fa-trophy"></i> 
                        <?php echo e($achievement->level); ?>

                    </p>
                    <p class="text-muted">
                        <i class="fas fa-user"></i> 
                        <?php echo e($achievement->participant); ?>

                    </p>
                    <p class="card-text"><?php echo e(Str::limit($achievement->description, 100)); ?></p>
                </div>
            </div>
        </div>
        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
        <div class="col-12">
            <div class="alert alert-info text-center">
                Belum ada data prestasi.
            </div>
        </div>
        <?php endif; ?>
    </div>

    <div class="d-flex justify-content-center mt-4">
        <?php echo e($achievements->links()); ?>

    </div>
</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.website', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\xampp\htdocs\webplp\resources\views/website/prestasi/all.blade.php ENDPATH**/ ?>